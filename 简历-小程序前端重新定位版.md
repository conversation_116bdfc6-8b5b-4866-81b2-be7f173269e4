# 潘慧文
**小程序开发工程师 | Vue前端开发工程师**

📱 15354872767 (微信同号) |✉️ <EMAIL> |📍 北京 |🎂 1992年2月 |🧑🏻‍💻 男

---

## 💼 工作经验

### 小程序开发工程师 | 内蒙古博睿达网络科技有限公司
**2024年6月 - 至今 (在职)**

#### 核心项目成果
- **特教公益小程序平台** (2024至今)
  - 与厦门特教基金会合作，采用uni-app框架开发跨平台公益小程序
  - 实现标准模块化设计，支持站点配置和内容结构化配置
  - 兼容用户、管理员双身份，涵盖益卖商城、捐赠系统、社区活动、IM等功能
  - **技术架构**: uni-app + Vue.js + Pinia状态管理 + 组件化开发
  - **技术难点**: 多身份权限控制、模块化配置系统、跨平台兼容性优化
  - **成果**: 成功上线微信小程序、支付宝小程序，获得基金会认可，提升公益服务数字化水平

- **企业内部服务小程序矩阵** (2024至今)
  - 开发集团企业内部服务系统和员工优惠购平台
  - 包含内部报修、会议室预定、餐饮预订等效率工具
  - **技术架构**: 微信原生小程序 + 云开发 + 企业微信集成
  - **技术难点**: 企业级权限管理、多业务模块集成、数据安全保障
  - **成果**: 提升企业内部运营效率30%+，获得客户好评

#### 技术架构与项目管理
- **小程序开发**: uni-app跨平台开发、微信原生小程序、支付宝小程序
- **前端技术**: Vue.js组件化开发、Pinia状态管理、模块化配置
- **架构设计**: 标准化模块设计，内容结构化配置，可复用组件库
- **项目管理**: 主导小程序项目开发，定制化企业内部服务解决方案

#### 主要产品线
- 特教公益类小程序（与厦门特教基金会合作）
- 企业内部服务小程序矩阵
- 定制化内购小程序平台

### 前端开发工程师 | 北京牛投邦科技咨询有限公司
**2018年8月 - 2024年4月 (5年8个月)**

#### 核心项目成果
- **多角色权限内容访问控制系统(RBAC)** (2020-2024)
  - 主导多家头部基金客户端从单角色升级为多角色多权限架构
  - **系统设计**: 基于RBAC模型，设计用户-角色-权限三层架构体系
  - **技术实现**: Vue.js + Pinia实现前端权限控制，动态路由和菜单生成
  - **核心功能**: 
    * 角色动态切换：支持用户在多个角色间无缝切换，保持会话状态
    * 权限粒度控制：页面级、功能级、数据级三层权限控制
    * 权限判别引擎：通过策略模式实现7种不同权限判别动作的统一管理
    * 状态同步机制：解决多角色切换时的状态管理和数据缓存问题
  - **业务痛点解决**: 
    * 解决金融行业复杂的多角色业务场景需求
    * 提升用户操作效率，减少重复登录和权限申请流程
    * 保障数据安全，实现细粒度的访问控制
  - **技术难点**: 多角色动态切换、权限判别逻辑、状态同步、数据缓存
  - **成果**: 项目如期交付，获得客户高度认可，为公司节省开发成本40%+

- **前端性能优化专项** (2020-2024)
  - **优化目标**: 解决应用启动慢、页面加载卡顿、用户体验差等问题
  - **优化策略**: 
    * 代码分包：使用Webpack动态import实现路由级代码分割，减少首屏加载体积50%+
    * 预加载机制：实现关键资源预加载和智能预测加载，提升页面切换速度
    * 启动流程优化：优化Vue应用初始化流程，移除非必要的同步操作
    * 缓存策略：实施多层缓存机制（浏览器缓存、本地存储、内存缓存）
    * 渲染优化：使用虚拟滚动、懒加载、骨架屏等技术优化用户体验
  - **技术手段**:
    * Vue.js性能优化：使用keep-alive、v-memo、异步组件等
    * Webpack配置优化：splitChunks、tree-shaking、压缩优化
    * 网络优化：HTTP/2、CDN、资源压缩、请求合并
  - **量化成果**: 
    * 应用初始化时间减少60%+（从8秒降至3秒）
    * 页面首屏渲染时间减少45%+
    * 用户满意度提升35%+，页面跳出率降低25%+

#### 技术架构与项目管理
- **前端开发**: Vue.js全家桶开发、组件化架构设计、状态管理优化
- **小程序开发**: 微信原生小程序、Taro、uni-app多平台开发，模板化架构设计
- **后台管理**: Vue.js + Element UI管理系统开发，完整的前后端分离架构
- **项目模板化**: 主导基于Vue.js的项目模板开发，提升团队开发效率50%+

#### 主要产品线
- 智能营销获客管理系统（Vue.js + Element UI）
- 栗子理财师、理财顾问云（Vue.js前端 + 小程序）
- 获客排行榜小程序、鹰眼获客能手小程序（Taro + uni-app）
- 多家头部基金公司前端解决方案

### 前端开发工程师 | 北京天云智慧科技有限公司
**2017年6月 - 2018年8月 (1年2个月)**

#### 核心项目成果
- **内部招聘管理系统** (2017-2018)
  - 独立开发内部招聘客户端和招聘管理端
  - **技术栈**: Vue.js + Element UI + 移动端H5开发
  - **技术难点**: 独立负责前端架构设计、前后端数据交互、响应式设计
  - **成果**: 成功交付完整的招聘管理系统，提升企业招聘效率40%+

#### 技术架构与项目管理
- **前端开发**: Vue.js全栈开发，涵盖PC端和移动端H5
- **项目管理**: 独立负责产品设计、开发、测试、上线全流程
- **技术栈**: Vue.js、JavaScript ES6+、响应式设计

#### 主要产品线
- 内部招聘管理系统（PC端 + 移动端H5）
- 招聘数据分析平台

---

## 🛠️ 技术栈

### 小程序开发
- **跨平台框架**: uni-app (3年+)、Taro (2年+)
- **原生小程序**: 微信小程序 (4年+)、支付宝小程序
- **开发工具**: 微信开发者工具、HBuilderX、Taro CLI
- **核心能力**: 跨平台适配、性能优化、组件化开发、云开发集成

### 前端技术
- **核心框架**: Vue.js (5年+)、Vue3 Composition API、Pinia
- **UI框架**: Element UI/Plus、Vant、Ant Design Vue
- **构建工具**: Webpack、Vite、Vue CLI
- **语言技能**: JavaScript (ES6+)、TypeScript、HTML5、CSS3/SCSS

### 开发工具与流程
- **IDE**: WebStorm、VS Code、HBuilderX
- **版本控制**: Git (团队协作、代码审查)
- **项目管理**: 敏捷开发、结果导向的项目交付
- **测试工具**: Jest、Vue Test Utils

### 核心能力
- 数据结构与算法、设计模式应用
- 独立开发能力、代码规范化
- 性能优化、架构设计
- 跨平台开发、响应式设计

---

## 🏢 行业经验

**金融科技领域** (5年+经验)
- 财富管理、证券、保险、基金、理财产品
- 熟悉金融业务流程和合规要求
- 具备完整的金融产品开发生命周期经验
- 擅长复杂权限系统和数据安全保障

**教育科技与公益领域**
- 特教公益小程序开发与运营
- 企业定制化服务解决方案
- 内部效率工具开发

---

## 🎯 核心优势

### 技术能力
- 擅长小程序开发和Vue.js前端架构设计
- 具备从0到1的产品开发经验
- 熟悉复杂业务场景的技术解决方案设计
- 精通跨平台开发和性能优化

### 问题解决能力
- 成功设计并实现多角色权限内容访问控制系统(RBAC)
- 通过技术手段显著提升产品性能和用户体验（60%+性能提升）
- 具备较强的技术难点攻克能力和系统性思维

### 团队协作
- 良好的沟通协调能力，跨部门合作经验丰富
- 责任心强，以结果为导向的工作方式
- 有内部技术分享经验，推动团队技术成长

---

## 🎓 教育背景

**本科学士学位** | 软件工程专业 | 2013.9 - 2017.6
内蒙古财经大学

---

## 📋 求职信息

- **期望职位**: 小程序开发工程师 / Vue前端开发工程师
- **工作地点**: 北京
- **到岗时间**: 5-7个工作日
- **工作年限**: 8年 (2016年6月至今)
