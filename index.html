<!doctype html>
<html>
<head>
<meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'>

<link href='https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap' rel='stylesheet' type='text/css' /><style type='text/css'>html {overflow-x: initial !important;}:root { --bg-color: #ffffff; --text-color: #333333; --select-text-bg-color: #B5D6FC; --select-text-font-color: auto; --monospace: "<PERSON><PERSON> Console",Consolas,"Courier",monospace; --title-bar-height: 20px; }
.mac-os-11 { --title-bar-height: 28px; }
html { font-size: 14px; background-color: var(--bg-color); color: var(--text-color); font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; -webkit-font-smoothing: antialiased; }
h1, h2, h3, h4, h5 { white-space: pre-wrap; }
body { margin: 0px; padding: 0px; height: auto; inset: 0px; font-size: 1rem; line-height: 1.428571; overflow-x: hidden; background: inherit; }
iframe { margin: auto; }
a.url { word-break: break-all; }
a:active, a:hover { outline: 0px; }
.in-text-selection, ::selection { text-shadow: none; background: var(--select-text-bg-color); color: var(--select-text-font-color); }
#write { margin: 0px auto; height: auto; width: inherit; word-break: normal; overflow-wrap: break-word; position: relative; white-space: normal; overflow-x: visible; padding-top: 36px; }
#write.first-line-indent p { text-indent: 2em; }
#write.first-line-indent li p, #write.first-line-indent p * { text-indent: 0px; }
#write.first-line-indent li { margin-left: 2em; }
.for-image #write { padding-left: 8px; padding-right: 8px; }
body.typora-export { padding-left: 30px; padding-right: 30px; }
.typora-export .footnote-line, .typora-export li, .typora-export p { white-space: pre-wrap; }
.typora-export .task-list-item input { pointer-events: none; }
@media screen and (max-width: 500px) {
  body.typora-export { padding-left: 0px; padding-right: 0px; }
  #write { padding-left: 20px; padding-right: 20px; }
}
#write li > figure:last-child { margin-bottom: 0.5rem; }
#write ol, #write ul { position: relative; }
img { max-width: 100%; vertical-align: middle; image-orientation: from-image; }
button, input, select, textarea { color: inherit; font-style: inherit; font-variant-caps: inherit; font-weight: inherit; font-width: inherit; font-size: inherit; line-height: inherit; font-family: inherit; font-size-adjust: inherit; font-kerning: inherit; font-variant-alternates: inherit; font-variant-ligatures: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-position: inherit; font-variant-emoji: inherit; font-feature-settings: inherit; font-optical-sizing: inherit; font-variation-settings: inherit; }
input[type="checkbox"], input[type="radio"] { line-height: normal; padding: 0px; }
*, ::after, ::before { box-sizing: border-box; }
#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre { width: inherit; }
#write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p { position: relative; }
p { line-height: inherit; }
h1, h2, h3, h4, h5, h6 { break-after: avoid-page; break-inside: avoid; orphans: 4; }
p { orphans: 4; }
h1 { font-size: 2rem; }
h2 { font-size: 1.8rem; }
h3 { font-size: 1.6rem; }
h4 { font-size: 1.4rem; }
h5 { font-size: 1.2rem; }
h6 { font-size: 1rem; }
.md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p { margin-top: 1rem; margin-bottom: 1rem; }
.hidden { display: none; }
.md-blockmeta { color: rgb(204, 204, 204); font-weight: 700; font-style: italic; }
a { cursor: pointer; }
sup.md-footnote { padding: 2px 4px; background-color: rgba(238, 238, 238, 0.7); color: rgb(85, 85, 85); border-radius: 4px; cursor: pointer; }
sup.md-footnote a, sup.md-footnote a:hover { color: inherit; text-transform: inherit; text-decoration: inherit; }
#write input[type="checkbox"] { cursor: pointer; width: inherit; height: inherit; }
figure { overflow-x: auto; margin: 1.2em 0px; max-width: calc(100% + 16px); padding: 0px; }
figure > table { margin: 0px; }
thead, tr { break-inside: avoid; break-after: auto; }
thead { display: table-header-group; }
table { border-collapse: collapse; border-spacing: 0px; width: 100%; overflow: auto; break-inside: auto; text-align: left; }
table.md-table td { min-width: 32px; }
.CodeMirror-gutters { border-right-width: 0px; border-right-style: none; border-right-color: currentcolor; background-color: inherit; }
.CodeMirror-linenumber { -webkit-user-select: none; }
.CodeMirror { text-align: left; }
.CodeMirror-placeholder { opacity: 0.3; }
.CodeMirror pre { padding: 0px 4px; }
.CodeMirror-lines { padding: 0px; }
div.hr:focus { cursor: none; }
#write pre { white-space: pre-wrap; }
#write.fences-no-line-wrapping pre { white-space: pre; }
#write pre.ty-contain-cm { white-space: normal; }
.CodeMirror-gutters { margin-right: 4px; }
.md-fences { font-size: 0.9rem; display: block; break-inside: avoid; text-align: left; overflow: visible; white-space: pre; background: inherit; position: relative !important; }
.md-fences-adv-panel { width: 100%; margin-top: 10px; text-align: center; padding-top: 0px; padding-bottom: 8px; overflow-x: auto; }
#write .md-fences.mock-cm { white-space: pre-wrap; }
.md-fences.md-fences-with-lineno { padding-left: 0px; }
#write.fences-no-line-wrapping .md-fences.mock-cm { white-space: pre; overflow-x: auto; }
.md-fences.mock-cm.md-fences-with-lineno { padding-left: 8px; }
.CodeMirror-line, twitterwidget { break-inside: avoid; }
svg { break-inside: avoid; }
.footnotes { opacity: 0.8; font-size: 0.9rem; margin-top: 1em; margin-bottom: 1em; }
.footnotes + .footnotes { margin-top: 0px; }
.md-reset { margin: 0px; padding: 0px; border: 0px; outline: 0px; vertical-align: top; background: 0px 0px; text-decoration: none; text-shadow: none; float: none; position: static; width: auto; height: auto; white-space: nowrap; cursor: inherit; line-height: normal; font-weight: 400; text-align: left; box-sizing: content-box; direction: ltr; }
li div { padding-top: 0px; }
blockquote { margin: 1rem 0px; }
li .mathjax-block, li p { margin: 0.5rem 0px; }
li blockquote { margin: 1rem 0px; }
li { margin: 0px; position: relative; }
blockquote > :last-child { margin-bottom: 0px; }
blockquote > :first-child, li > :first-child { margin-top: 0px; }
.footnotes-area { color: rgb(136, 136, 136); margin-top: 0.714rem; padding-bottom: 0.143rem; white-space: normal; }
#write .footnote-line { white-space: pre-wrap; }
@media print {
  body, html { border: 1px solid transparent; height: 99%; break-after: avoid; break-before: avoid; font-variant-ligatures: no-common-ligatures; }
  #write { margin-top: 0px; border-color: transparent !important; padding-top: 0px !important; padding-bottom: 0px !important; }
  .typora-export * { print-color-adjust: exact; }
  .typora-export #write { break-after: avoid; }
  .typora-export #write::after { height: 0px; }
  .is-mac table { break-inside: avoid; }
  #write > p:nth-child(1) { margin-top: 0px; }
  .typora-export-show-outline .typora-export-sidebar { display: none; }
  figure { overflow-x: visible; }
}
.footnote-line { margin-top: 0.714em; font-size: 0.7em; }
a img, img a { cursor: pointer; }
pre.md-meta-block { font-size: 0.8rem; min-height: 0.8rem; white-space: pre-wrap; background: rgb(204, 204, 204); display: block; overflow-x: hidden; }
p > .md-image:only-child:not(.md-img-error) img, p > img:only-child { display: block; margin: auto; }
#write.first-line-indent p > .md-image:only-child:not(.md-img-error) img { left: -2em; position: relative; }
p > .md-image:only-child { display: inline-block; width: 100%; }
#write .MathJax_Display { margin: 0.8em 0px 0px; }
.md-math-block { width: 100%; }
.md-math-block:not(:empty)::after { display: none; }
.MathJax_ref { fill: currentcolor; }
[contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus { outline: 0px; box-shadow: none; }
.md-task-list-item { position: relative; list-style-type: none; }
.task-list-item.md-task-list-item { padding-left: 0px; }
.md-task-list-item > input { position: absolute; top: 0px; left: 0px; margin-left: -1.2em; margin-top: calc(1em - 10px); border: medium; }
.math { font-size: 1rem; }
.md-toc { min-height: 3.58rem; position: relative; font-size: 0.9rem; border-radius: 10px; }
.md-toc-content { position: relative; margin-left: 0px; }
.md-toc-content::after, .md-toc::after { display: none; }
.md-toc-item { display: block; color: rgb(65, 131, 196); }
.md-toc-item a { text-decoration: none; }
.md-toc-inner:hover { text-decoration: underline; }
.md-toc-inner { display: inline-block; cursor: pointer; }
.md-toc-h1 .md-toc-inner { margin-left: 0px; font-weight: 700; }
.md-toc-h2 .md-toc-inner { margin-left: 2em; }
.md-toc-h3 .md-toc-inner { margin-left: 4em; }
.md-toc-h4 .md-toc-inner { margin-left: 6em; }
.md-toc-h5 .md-toc-inner { margin-left: 8em; }
.md-toc-h6 .md-toc-inner { margin-left: 10em; }
@media screen and (max-width: 48em) {
  .md-toc-h3 .md-toc-inner { margin-left: 3.5em; }
  .md-toc-h4 .md-toc-inner { margin-left: 5em; }
  .md-toc-h5 .md-toc-inner { margin-left: 6.5em; }
  .md-toc-h6 .md-toc-inner { margin-left: 8em; }
}
a.md-toc-inner { font-size: inherit; font-style: inherit; font-weight: inherit; line-height: inherit; }
.footnote-line a:not(.reversefootnote) { color: inherit; }
.reversefootnote { font-family: ui-monospace, sans-serif; }
.md-attr { display: none; }
.md-fn-count::after { content: "."; }
code, pre, samp, tt { font-family: var(--monospace); }
kbd { margin: 0px 0.1em; padding: 0.1em 0.6em; font-size: 0.8em; color: rgb(36, 39, 41); background: rgb(255, 255, 255); border: 1px solid rgb(173, 179, 185); border-radius: 3px; box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset; white-space: nowrap; vertical-align: middle; }
.md-comment { color: rgb(162, 127, 3); opacity: 0.6; font-family: var(--monospace); }
code { text-align: left; vertical-align: initial; }
a.md-print-anchor { white-space: pre !important; border-width: medium !important; border-style: none !important; border-color: currentcolor !important; display: inline-block !important; position: absolute !important; width: 1px !important; right: 0px !important; outline: 0px !important; background: 0px 0px !important; text-decoration: initial !important; text-shadow: initial !important; }
.os-windows.monocolor-emoji .md-emoji { font-family: "Segoe UI Symbol", sans-serif; }
.md-diagram-panel > svg { max-width: 100%; }
[lang="flow"] svg, [lang="mermaid"] svg { max-width: 100%; height: auto; }
[lang="mermaid"] .node text { font-size: 1rem; }
table tr th { border-bottom-width: 0px; border-bottom-style: none; border-bottom-color: currentcolor; }
video { max-width: 100%; display: block; margin: 0px auto; }
iframe { max-width: 100%; width: 100%; border: medium; }
.highlight td, .highlight tr { border: 0px; }
mark { background: rgb(255, 255, 0); color: rgb(0, 0, 0); }
.md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong { color: inherit; }
.md-expand mark .md-meta { opacity: 0.3 !important; }
mark .md-meta { color: rgb(0, 0, 0); }
@media print {
  .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 { break-inside: avoid; }
}
.md-diagram-panel .messageText { stroke: none !important; }
.md-diagram-panel .start-state { fill: var(--node-fill); }
.md-diagram-panel .edgeLabel rect { opacity: 1 !important; }
.md-fences.md-fences-math { font-size: 1em; }
.md-fences-advanced:not(.md-focus) { padding: 0px; white-space: nowrap; border: 0px; }
.md-fences-advanced:not(.md-focus) { background: inherit; }
.typora-export-show-outline .typora-export-content { max-width: 1440px; margin: auto; display: flex; flex-direction: row; }
.typora-export-sidebar { width: 300px; font-size: 0.8rem; margin-top: 80px; margin-right: 18px; }
.typora-export-show-outline #write { --webkit-flex: 2; flex: 2 1 0%; }
.typora-export-sidebar .outline-content { position: fixed; top: 0px; max-height: 100%; overflow: hidden auto; padding-bottom: 30px; padding-top: 60px; width: 300px; }
@media screen and (max-width: 1024px) {
  .typora-export-sidebar, .typora-export-sidebar .outline-content { width: 240px; }
}
@media screen and (max-width: 800px) {
  .typora-export-sidebar { display: none; }
}
.outline-content li, .outline-content ul { margin-left: 0px; margin-right: 0px; padding-left: 0px; padding-right: 0px; list-style: none; overflow-wrap: anywhere; }
.outline-content ul { margin-top: 0px; margin-bottom: 0px; }
.outline-content strong { font-weight: 400; }
.outline-expander { width: 1rem; height: 1.428571rem; position: relative; display: table-cell; vertical-align: middle; cursor: pointer; padding-left: 4px; }
.outline-expander::before { content: ""; position: relative; font-family: Ionicons; display: inline-block; font-size: 8px; vertical-align: middle; }
.outline-item { padding-top: 3px; padding-bottom: 3px; cursor: pointer; }
.outline-expander:hover::before { content: ""; }
.outline-h1 > .outline-item { padding-left: 0px; }
.outline-h2 > .outline-item { padding-left: 1em; }
.outline-h3 > .outline-item { padding-left: 2em; }
.outline-h4 > .outline-item { padding-left: 3em; }
.outline-h5 > .outline-item { padding-left: 4em; }
.outline-h6 > .outline-item { padding-left: 5em; }
.outline-label { cursor: pointer; display: table-cell; vertical-align: middle; text-decoration: none; color: inherit; }
.outline-label:hover { text-decoration: underline; }
.outline-item:hover { border-color: rgb(245, 245, 245); background-color: var(--item-hover-bg-color); }
.outline-item:hover { margin-left: -28px; margin-right: -28px; border-left-width: 28px; border-left-style: solid; border-left-color: transparent; border-right-width: 28px; border-right-style: solid; border-right-color: transparent; }
.outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before { display: none; }
.outline-item-open > .outline-item > .outline-expander::before { content: ""; }
.outline-children { display: none; }
.info-panel-tab-wrapper { display: none; }
.outline-item-open > .outline-children { display: block; }
.typora-export .outline-item { padding-top: 1px; padding-bottom: 1px; }
.typora-export .outline-item:hover { margin-right: -8px; border-right-width: 8px; border-right-style: solid; border-right-color: transparent; }
.typora-export .outline-expander::before { content: "+"; font-family: inherit; top: -1px; }
.typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before { content: "−"; }
.typora-export-collapse-outline .outline-children { display: none; }
.typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children { display: block; }
.typora-export-no-collapse-outline .outline-expander::before { content: "" !important; }
.typora-export-show-outline .outline-item-active > .outline-item .outline-label { font-weight: 700; }
.md-inline-math-container mjx-container { zoom: 0.95; }
mjx-container { break-inside: avoid; }
.md-alert.md-alert-note { border-left-color: rgb(9, 105, 218); }
.md-alert.md-alert-important { border-left-color: rgb(130, 80, 223); }
.md-alert.md-alert-warning { border-left-color: rgb(154, 103, 0); }
.md-alert.md-alert-tip { border-left-color: rgb(31, 136, 61); }
.md-alert.md-alert-caution { border-left-color: rgb(207, 34, 46); }
.md-alert { padding: 0px 1em; margin-bottom: 16px; color: inherit; border-left-width: 0.25em; border-left-style: solid; border-left-color: rgb(0, 0, 0); }
.md-alert-text-note { color: rgb(9, 105, 218); }
.md-alert-text-important { color: rgb(130, 80, 223); }
.md-alert-text-warning { color: rgb(154, 103, 0); }
.md-alert-text-tip { color: rgb(31, 136, 61); }
.md-alert-text-caution { color: rgb(207, 34, 46); }
.md-alert-text { font-size: 0.9rem; font-weight: 700; }
.md-alert-text svg { fill: currentcolor; position: relative; top: 0.125em; margin-right: 1ch; overflow: visible; }
.md-alert-text-container::after { content: attr(data-text); text-transform: capitalize; pointer-events: none; margin-right: 1ch; }


/*快速自定义配置*/
:root {
    --monospace: "Iosevka Curly", "JetBrains Mono", "Fira Code", "Cascadia Code", Menlo, "Ubuntu Mono", Consolas, HYZhengYuan; /*代码字体*/
    --text-font: var(--monospace); /*正文字体*/
    --title-font: var(--monospace); /*标题字体*/
    --latex-font: var(--monospace); /*LaTeX字体(不含英语)*/
    --text-line-height: 1.6; /*正文行间距*/
    --code-line-height: 1.6; /*代码块行间距*/
    --p-spacing: 0.8rem; /*段间距*/
    --file-tree-text-size: 1.1rem; /*文件树大小*/
    --toc-text-size: 1rem; /*大纲大小*/
    --text-size: 14px; /*字体大小 推荐配置: 应用设置-外观-字体大小*/
}/*
 * MIT License
 *
 * Copyright (c) 2023 劉強東 https://github.com/liangjingkanji
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
@import url();
@include-when-export url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');

:root {
  --text-color: #19191c;
  --bg-color: #fffefe;
  --control-text-color: var(--text-color);
  --meta-content-color: var(--text-color);
  --active-file-border-color: var(--drake-accent);
  --rawblock-edit-panel-bd: var(--code-block-bg-color);
  --item-hover-bg-color: #e5e5e596;
  --active-file-bg-color: var(--item-hover-bg-color);

  --blur-text-color: rgba(51, 51, 51, 0.5);
  --drake-accent: #307fff;
  --drake-highlight: #19191c;
  --a-color: var(--text-color);
  --variable-color: var(--drake-highlight);
  --outline-active-color: var(--text-color);
  --code-block-bg-color: #2b2b2b;
  --code-block-color: #a9b7c6;
  --title-color: #273849;
  --blockquote-border-color: rgba(255, 255, 255, 0);
  --blockquote-color: #19191cb3;
  --blockquote-bg-color: #f4f4f4;
  --strong-color: #000000;
  --h2-underline-color: var(--title-color);
  --horizontal-divider-color: var(--title-color);
  --height-light-color: var(--drake-highlight);
  --height-light-border-color: rgb(25, 25, 28, 0.3);
  --yaml-color: #777777;
  --yaml-bg-color: #f7f7f7;
  --footnotes-bg-color: #3c3d3e;
  --footnotes-highlight: #ffd760;
  --table-border-color: #dfdfdf;
  --table-header-bg-color: transparent;
  --table-bg-color: transparent;
  --table-n2-bg-color: transparent;
  --input-bg-color: var(--item-hover-bg-color);
  --btn-hover-bg-color: var(--item-hover-bg-color);
  --checkbox-checked: url("data:image/svg+xml,%3Csvg class='icon' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='200' height='200'%3E%3Cpath d='M425.984 726.016l384-384-59.99-61.995-324.01 324.011-152.021-152.021L213.973 512zm384-598.016q36.01 0 61.013 25.984T896 213.974v596.01q0 34.005-25.003 59.99t-61.013 25.983h-596.01q-36.011 0-61.014-25.984t-25.003-59.989v-596.01q0-34.006 25.003-59.99T213.973 128h596.011z' fill='%2319191C'/%3E%3C/svg%3E");
  --checkbox-unchecked: url("data:image/svg+xml,%3Csvg class='icon' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='200' height='200'%3E%3Cpath d='M810.667 213.333v597.334H213.333V213.333h597.334m0-85.333H213.333C166.4 128 128 166.4 128 213.333v597.334C128 857.6 166.4 896 213.333 896h597.334C857.6 896 896 857.6 896 810.667V213.333C896 166.4 857.6 128 810.667 128z' fill='%23333333'/%3E%3C/svg%3E");
}

html {
  font-size: var(--text-size);
}

body {
  font-family: var(--text-font) !important;
  color: var(--text-color);
  -webkit-font-feature-settings: "liga" on, "calt" on;
  -webkit-font-smoothing: subpixel-antialiased;
  text-rendering: optimizeLegibility;
  letter-spacing: 0;
  margin: 0;
  overflow-x: hidden;
}

p {
  line-height: var(--text-line-height);
}

/*code block*/
.md-fences {
  font-size: 1rem;
  padding: 0.5rem !important;
  border-radius: 2px;
  word-wrap: normal;
  background-color: var(--code-block-bg-color);
  color: var(--code-block-color);
  border: none;
  line-height: var(--code-line-height);
}

.on-focus-mode .CodeMirror.cm-s-inner:not(.CodeMirror-focused) *,
.on-focus-mode
  .md-fences.md-focus
  .CodeMirror-code
  > :not(.CodeMirror-activeline)
  *,
.on-focus-mode
  .md-htmlblock.md-focus
  .CodeMirror-code
  > :not(.CodeMirror-activeline)
  * {
  color: #909090 !important;
}

/*inline latex*/
.MathJax {
  font-size: 120% !important;
}

.MathJax text,
.MathJax use {
  font-family: var(--latex-font);
}

/*math-block latex*/
.md-math-block .MathJax {
  font-size: 130% !important;
}

/*mermaid*/
[id^="mermaidChart"] .cluster rect {
  fill: var(--table-n2-bg-color) !important;
  stroke: var(--table-border-color) !important;
}

[id^="mermaidChart"] .grid .tick text,
[id^="mermaidChart"] .titleText {
  fill: var(--text-color) !important;
}

[id^="mermaidChart"] .clusters span.nodeLabel {
  color: var(--text-color) !important;
  line-height: 1.8rem;
}

[mermaid-type="journey"] line {
  stroke: #7a7a7a !important;
}

[mermaid-type="journey"] .label {
  color: #333 !important;
}

[id^="mermaidChart"] .relationshipLabelBox {
  fill: var(--bg-color) !important;
  opacity: 1 !important;
  background-color: var(--bg-color) !important;
}

[id^="mermaidChart"] .legend {
  fill: var(--text-color) !important;
}

[id^="mermaidChart"] g.label {
  font-size: 1rem !important;
}

[id^="mermaidChart"] line.divider {
  stroke: var(--table-border-color) !important;
}

[id^="mermaidChart"] span.nodeLabel {
  color: var(--code-block-color) !important;
  line-height: 1.8rem;
}

tspan {
  color: var(--text-color);
}

[id^="mermaidChart"] .entityLabel {
  fill: var(--code-block-color) !important;
}

[id^="mermaidChart"] {
  fill: var(--text-color) !important;
}

[id^="mermaidChart"] rect.rect {
  fill: rgba(175, 255, 212, 0.3) !important;
}

.md-diagram-panel-preview text.actor > tspan {
  /*方块文字*/
  fill: var(--code-block-color) !important;
  stroke: none !important;
  font-family: var(--text-font) !important;
  font-size: 1rem !important;
}

.md-diagram-panel-preview .actor,
.md-diagram-panel-preview .entityBox {
  /*方块*/
  stroke: var(--table-border-color) !important;
  fill: var(--code-block-bg-color) !important;
}

.md-diagram-panel-preview .actor-line {
  /*竖线*/
  stroke: var(--text-color) !important;
  stroke-width: 1px;
}

.md-diagram-panel-preview .messageLine0 {
  /*横线*/
  stroke-width: 1.5;
  stroke-dasharray: none;
  stroke: var(--text-color) !important;
}

.md-diagram-panel-preview .messageLine1 {
  /*虚线*/
  stroke-width: 1.5 !important;
  stroke-dasharray: 2, 2 !important;
  stroke: var(--text-color) !important;
}

.md-diagram-panel-preview .messageText {
  /*描述文字*/
  fill: var(--text-color) !important;
  font-family: var(--text-font) !important;
  font-size: 1rem !important;
}

.md-diagram-panel-preview .activation0 {
  /*长方形*/
  fill: #e6e6e6 !important;
  stroke: var(--text-color) !important;
}

.md-diagram-panel-preview .labelText,
.md-diagram-panel-preview .labelText > tspan {
  /*循环标记*/
  fill: var(--code-block-color) !important;
  font-family: var(--text-font) !important;
  font-size: 1rem !important;
  dominant-baseline: unset;
  alignment-baseline: unset;
}

.md-diagram-panel-preview .labelBox {
  /*循环标记背景*/
  stroke: var(--table-border-color) !important;
  fill: var(--code-block-bg-color) !important;
}

.md-diagram-panel-preview .loopLine {
  /*循环标记虚线*/
  stroke: var(--text-color) !important;
}

.md-diagram-panel-preview .loopText,
.md-diagram-panel-preview .loopText > tspan {
  /*循环名称*/
  fill: var(--text-color) !important;
  font-size: 1rem !important;
}

.md-diagram-panel-preview .sequenceNumber {
  /*序号*/
  fill: var(--bg-color) !important;
}

pre.md-fences-advanced.md-focus .md-fences-adv-panel {
  border: none;
}

.md-diagram-panel-preview .edgePath .path {
  /*箭头*/
  stroke: var(--text-color) !important;
}

.md-diagram-panel-preview .edgeLabel rect {
  /*条件文字背景*/
  fill: var(--bg-color) !important;
}

.md-diagram-panel-preview .edgeLabel span {
  /*条件文字*/
  color: var(--text-color) !important;
  background: var(--bg-color) !important;
}

.md-diagram-panel-preview .node rect,
.md-diagram-panel-preview .node circle,
.md-diagram-panel-preview .node ellipse,
.md-diagram-panel-preview .node polygon,
.md-diagram-panel-preview .node path {
  /*形状*/
  stroke: var(--table-border-color) !important;
  fill: var(--code-block-bg-color) !important;
}

#write .md-diagram-panel .md-diagram-panel-preview div {
  /*形状内文字*/
  color: var(--code-block-color);
  font-family: var(--text-font) !important;
  font-size: 1rem !important;
}

/*code snippet*/
#write code,
tt {
  margin: 0 2px;
  color: var(--drake-highlight);
  background-color: #19191c0d;
  padding: 3px 6px;
  border-radius: 4px;
}

/*variable*/
var {
  color: var(--variable-color);
  font-weight: bold;
}

/*raw block*/
.md-rawblock-control:not(.md-rawblock-tooltip) {
  border-radius: 2px 0 2px 2px;
  padding: 0.2rem !important;
}

.md-rawblock:hover > .md-rawblock-container {
  background: none;
}

.md-rawblock-input {
  font-size: 1rem;
}

.md-rawblock-tooltip-btn:hover {
  background: none;
}

.md-rawblock:hover > .md-rawblock-tooltip {
  border-radius: 2px 2px 0 0;
  margin-bottom: 2px !important;
}

.md-rawblock-tooltip.md-rawblock-control {
  border-radius: 2px 2px 0 0;
  color: var(--code-block-color);
}

.md-rawblock-tooltip-name {
  color: var(--code-block-color);
  opacity: 1;
}

/*quote block*/
blockquote:before {
  display: block;
  position: absolute;
  content: "";
  width: 4px;
  left: 0;
  top: 0;
  height: 100%;
  background-color: var(--blockquote-border-color);
  border-radius: 2px;
}

blockquote {
  color: var(--blockquote-color);
  border-radius: 2px;
  padding: 16px;
  background-color: var(--blockquote-bg-color);
  position: relative;
  border-left: none;
}

strong {
  color: var(--strong-color);
  font-weight: bold;
}

#write blockquote strong {
  color: var(--blockquote-color);
}

/*link*/
#write a {
  color: var(--a-color);
  text-decoration: none;
}

#write h2 a .md-plain {
  border-bottom: 0.2rem solid rgb(25, 25, 28, 0.2);
}

.on-focus-mode #write a .md-plain,
.on-focus-mode .md-htmlblock-container a:hover {
  border-bottom: 0.1rem solid rgb(25, 25, 28, 0.2);
}

#write a .md-plain,
.md-htmlblock-container a:hover,
.on-focus-mode #write .md-focus a .md-plain,
.md-focus .md-htmlblock-container a:hover {
  border-bottom: 0.1rem solid rgb(25, 25, 28, 0.2);
}

[md-inline="link"] a {
  margin: 0 0.2rem;
}

a:any-link {
  color: var(--a-color);
}

img {
  border-left: none;
  border-right: none;
  vertical-align: baseline;
  border-radius: 2px;
}

#write {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 30px 100px;
}

#typora-source .CodeMirror-lines {
  max-width: 1200px;
}

#write > ul:first-child,
#write > ol:first-child {
  margin-top: 30px;
}

body > *:first-child {
  margin-top: 0 !important;
}

body > *:last-child {
  margin-bottom: 0 !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--title-font);
  position: relative;
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: bold;
  line-height: 1.4;
  cursor: text;
  color: var(--title-color);
}

h3.md-focus:before,
h4.md-focus:before,
h5.md-focus:before,
h6.md-focus:before {
  visibility: hidden;
}

h1 {
  font-size: 2rem;
  text-align: center;
  margin-top: 0;
}

h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
}

.on-focus-mode
  h2.md-end-block.md-heading:not(.md-focus):not(.md-focus-container):after {
  background-color: var(--blur-text-color) !important;
}

h3 {
  font-size: 1.4rem;
}

h4 {
  font-size: 1.2rem;
}

h5 {
  font-size: 1rem;
}

h6 {
  font-size: 1rem;
}

h1:hover a.anchor,
h2:hover a.anchor,
h3:hover a.anchor,
h4:hover a.anchor,
h5:hover a.anchor,
h6:hover a.anchor {
  text-decoration: none;
}

h1 tt,
h1 code {
  font-size: inherit !important;
}

h2 tt,
h2 code {
  font-size: inherit !important;
}

h3 tt,
h3 code {
  font-size: inherit !important;
}

h4 tt,
h4 code {
  font-size: inherit !important;
}

h5 tt,
h5 code {
  font-size: inherit !important;
}

h6 tt,
h6 code {
  font-size: inherit !important;
}

p,
blockquote,
ul,
ol,
dl,
table {
  margin: var(--p-spacing) 0;
}

li > ol,
li > ul {
  margin: 0 0;
}

li {
  margin: 0.5em 0;
}

hr {
  height: 2px;
  padding: 0;
  margin: 16px 0;
  background-color: var(--horizontal-divider-color);
  border: 0 none;
  overflow: hidden;
  box-sizing: content-box;
}

body > h2:first-child {
  margin-top: 0;
  padding-top: 0;
}

body > h1:first-child {
  margin-top: 0;
  padding-top: 0;
}

body > h1:first-child + h2 {
  margin-top: 0;
  padding-top: 0;
}

body > h3:first-child,
body > h4:first-child,
body > h5:first-child,
body > h6:first-child {
  margin-top: 0;
  padding-top: 0;
}

a:first-child h1,
a:first-child h2,
a:first-child h3,
a:first-child h4,
a:first-child h5,
a:first-child h6 {
  margin-top: 0;
  padding-top: 0;
}

h1 p,
h2 p,
h3 p,
h4 p,
h5 p,
h6 p {
  margin-top: 0;
}

li p.first {
  display: inline-block;
}

ul,
ol {
  padding-inline-start: 2em;
}

ul:first-child,
ol:first-child {
  margin-top: 0;
}

ul:last-child,
ol:last-child {
  margin-bottom: 0;
}

.ty-table-edit {
  margin-top: -1rem !important;
}

#write table {
  margin-top: 1rem;
  word-break: initial;
  background-color: var(--table-bg-color);
}

tbody tr {
  border-top: 0.1em solid var(--table-border-color);
  margin: 0;
  padding: 0;
}

table th {
  font-weight: bold;
  border-bottom: 0;
  margin: 0;
  padding: 20px 13px;
}

table td {
  border-top: 0.1em solid var(--table-border-color);
  margin: 0;
  padding: 20px 13px;
}

table thead {
  background-color: var(--table-header-bg-color);
}

table tr:nth-child(2n) {
  background-color: var(--table-n2-bg-color);
}

table tr th:first-child,
table tr td:first-child {
  margin-top: 0;
}

table tr th:last-child,
table tr td:last-child {
  margin-bottom: 0;
}

#write em {
  padding: 0 5px 0 2px;
}

/* height light */
#write mark {
  border: 0.1em solid var(--height-light-border-color);
  color: var(--height-light-color);
  background-color: transparent;
  padding: 0.3rem 0.6rem;
  border-radius: 2rem;
  margin: 0 0.2rem;
  font-size: 0.95rem;
}

/*shortcut*/
kbd {
  border: 0.1em solid rgba(25, 25, 28, 0.2);
  background: #19191c0d;
  color: var(--text-color);
  margin: 0 0.4rem;
  font-family: var(--text-font) !important;
  font-size: 0.95rem;
  padding: 0.3em 0.4em;
  border-radius: 0.4em;
  box-shadow: none;
}

#write del {
  padding: 1px 2px;
}

.md-task-list-item > input {
  margin-left: -1.3em;
}

@media print {
  html {
    font-size: 12px;
  }
  /* 避免标题被分页切断 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
    break-after: avoid;
    break-inside: avoid;
  }

  /* 避免表格、代码块、引用块整体被分页切断 */
  pre,
  table,
  blockquote,
  ul,
  ol,
  .md-fences,
  .md-table {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* 避免图片被分页切断 */
  img {
    page-break-inside: avoid;
    break-inside: avoid;
    max-width: 100%;
  }

  /* 增加每页的顶部和底部边距，防止内容贴边 */
  body {
    margin-top: 30px !important;
    margin-bottom: 30px !important;
  }

  /* 保证段落分页合理 */
  /* 设置段落中的最小行数，避免在分页时出现孤行（单独的行出现在页面顶部或底部） */
  p {
    orphans: 3;
    widows: 3;
  }

  /* 可选：导出 PDF 时隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}

/*YAML*/
#write pre.md-meta-block {
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.45;
  background-color: var(--yaml-bg-color);
  border: 0;
  border-radius: 2px;
  color: var(--yaml-color);
  margin-top: 0 !important;
}

.mathjax-block > .code-tooltip {
  bottom: 0.375rem;
}

#write > h3.md-focus:before {
  left: -1.5625rem;
  top: 0.375rem;
}

#write > h4.md-focus:before {
  left: -1.5625rem;
  top: 0.285714286rem;
}

#write > h5.md-focus:before {
  left: -1.5625rem;
  top: 0.285714286rem;
}

#write > h6.md-focus:before {
  left: -1.5625rem;
  top: 0.285714286rem;
}

.md-image > .md-meta {
  border-radius: 2px;
  font-family: initial;
  padding: 2px 0 0 4px;
  color: inherit;
}

.md-tag {
  color: inherit;
}

.md-toc {
  margin-top: 20px;
  padding-bottom: 20px;
}

.typora-quick-open-item {
  font-size: 1rem !important;
  height: 50px;
  padding-left: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
}

#typora-quick-open {
  box-shadow: 0 0 8px #00000045;
  padding: 0;
}

.ty-quick-open-category.ty-has-prev .ty-quick-open-category-title {
  border-top: none;
}

#typora-quick-open-input {
  margin: 8px;
  box-shadow: none;
  border-radius: 2px;
}

#typora-quick-open-input input {
  font-size: 1rem;
  box-shadow: none;
  padding-top: 2px;
  padding-left: 10px;
  padding-right: 10px;
  line-height: 32px;
  max-height: 32px;
  border: none;
}

.modal-dialog#typora-quick-open {
  border-radius: 8px;
}

.ty-quick-open-category-title {
  padding-left: 8px;
  color: #bebebe;
  font-size: 0.8rem;
  margin-bottom: 4px;
}

.typora-quick-open-item-path {
  font-size: 0.8rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 1px;
}

/*export background*/
.export-detail,
.export-item.active,
.export-items-list-control {
  background: var(--active-file-bg-color) !important;
}

/*source code mode*/
.cm-s-typora-default .CodeMirror-activeline-background,
.typora-sourceview-on #toggle-sourceview-btn {
  background: var(--active-file-bg-color);
}

/*search input*/
.form-control {
  border: none;
  border-radius: 2px;
  box-shadow: none;
}

#md-searchpanel .btn {
  border-radius: 2px;
}

#search-panel-replaceall-btn {
  padding-right: 5px !important;
  text-align: center !important;
}

#search-panel-replace-btn {
  text-align: center !important;
}

#md-searchpanel input {
  background: var(--input-bg-color);
  border-radius: 2px;
}

.searchpanel-search-option-btn {
  border-radius: 2px;
  border: none;
  background: transparent;
  color: var(--text-color);
}

.searchpanel-search-option-btn.active {
  background: var(--text-color);
  color: var(--bg-color);
}

.form-control:focus {
  box-shadow: none;
}

#md-notification:before {
  top: 10px;
}

/** focus mode */
.on-focus-mode blockquote {
  border-left-color: rgba(85, 85, 85, 0.12);
}

header,
.context-menu,
.megamenu-content,
footer {
  font-family: initial;
}

/*sidebar*/
.file-library-node.file-tree-node.file-node-root {
  font-size: var(--file-tree-text-size);
}

.file-node-content:hover .file-node-icon,
.file-node-content:hover .file-node-open-state {
  visibility: visible;
}

.file-node-content {
  display: flex;
  align-items: center;
}

.file-node-open-state {
  margin-right: 0.5rem;
}

.file-node-icon {
  margin-right: 0.5rem;
}

#typora-sidebar {
  font-size: inherit;
  font-family: var(--title-font);
  color: rgba(60, 60, 60, 0.7);
}

.sidebar-tabs {
  border-bottom: none;
}

.file-list-item-summary,
.file-list-item-parent-loc,
.file-list-item-time,
.file-list-item-summary {
  font-size: 0.9rem !important;
  font-family: var(--text-font);
}

.file-list-item-file-ext-part {
  display: none;
}

.outline-item {
  font-size: var(--toc-text-size);
}

/*footnotes mark*/
#write .md-footnote {
  background-color: inherit;
  color: var(--drake-highlight);
  font-size: 0.9rem;
  border-radius: 0.9rem;
  padding-left: 0;
}

#write .md-footnote:before {
  content: "[";
}

#write .md-footnote:after {
  content: "]";
}

/*footnotes content*/
.md-hover-tip .code-tooltip-content {
  border-radius: 2px;
}

/*footnotes title*/
span.md-def-name {
  padding-right: 3ch;
  padding-left: 0;
  position: relative;
  font-weight: normal;
}

/*footnotes desc*/
.footnotes {
  font-size: 1rem;
  font-weight: normal;
  color: var(--text-color);
  position: relative;
}

/*footnotes tooltip text*/
.code-tooltip-content .md-plain {
  font-size: 0.9rem;
  font-family: inherit;
}

.code-tooltip-content code {
  padding: 0 2px;
  font-family: inherit;
  color: var(--footnotes-highlight);
  background-color: inherit;
}

.code-tooltip-content a {
  color: var(--footnotes-highlight);
}

div.code-tooltip-content {
  box-shadow: 0 0 8px #00000045;
  background: var(--footnotes-bg-color);
}

.footnotes {
  opacity: 1;
}

.md-def-name:after {
  content: ". ^";
  color: var(--text-color);
}

.md-def-footnote .md-def-name:before {
  content: "";
  color: var(--text-color);
  position: absolute;
}

.md-def-name:before {
  content: "";
  color: var(--text-color);
  position: absolute;
}

.md-content.md-url,
.md-def-content.md-def-url.md-auto-disp {
  text-decoration: none;
  border-bottom: 0.1rem solid rgb(25, 25, 28, 0.2);
}

.CodeMirror-scroll::-webkit-scrollbar {
  display: none;
}

.file-list-item-summary {
  font-size: 1em;
}

.pin-outline #outline-content .outline-active strong,
.pin-outline .outline-active {
  font-weight: 500;
  color: var(--outline-active-color);
}

.file-list-item.active {
  border-left: 4px solid var(--drake-accent);
}

#md-searchpanel .btn:not(.close-btn):hover {
  box-shadow: none;
  background: var(--btn-hover-bg-color);
}

/*checkbox*/
#write input[type="checkbox"] {
  opacity: 0;
  height: 1.6rem;
  width: 1.6rem;
  margin-left: -2em;
  margin-top: 0;
  top: 0;
}

#write .ul-list li.md-task-list-item.task-list-done::before {
  content: "";
  background: var(--checkbox-checked) 0 0 no-repeat;
  background-size: 100%;
  display: inline-block;
  position: absolute;
  height: 1.6rem;
  width: 1.6rem;
  margin-left: -2em;
}

#write .ul-list li.md-task-list-item.task-list-not-done::before {
  content: "";
  background: var(--checkbox-unchecked) 0 0 no-repeat;
  background-size: 100%;
  display: inline-block;
  position: absolute;
  height: 1.6rem;
  width: 1.6rem;
  margin-left: -2em;
}

.task-list-item p {
  line-height: 1.6rem;
}

/*insert table*/
.btn {
  border-radius: 2px;
}

.modal-content {
  border-radius: 8px;
}

.btn-primary:hover,
.btn-primary:active {
  background-color: var(--btn-hover-bg-color);
  color: var(--drake-highlight);
}

.btn-primary {
  background-color: transparent;
  color: var(--drake-highlight);
}

.btn-default {
  background-color: transparent;
}

.btn:active {
  box-shadow: none;
  border-color: transparent;
}

.modal-footer {
  border-top: none;
}

#table-insert-col,
#table-insert-row {
  background: var(--input-bg-color);
  border-radius: 2px;
}

/*preference panel*/
#megamenu-content {
  background-image: none !important;
  background-color: var(--bg-color);
}

#top-titlebar {
  height: inherit;
  background-color: var(--bg-color);
}

#megamenu-menu-sidebar {
  background-color: var(--bg-color);
  color: var(--text-color);
}

.long-btn {
  width: inherit;
  min-width: 300px;
  border: 1px solid var(--text-color);
  border-radius: 6px;
}

.megamenu-menu-panel h1 {
  margin-bottom: 3rem;
  text-align: left;
}

.megamenu-menu-panel h1,
.megamenu-menu-panel h2 {
  font-weight: normal;
}

#recent-file-panel-search-input {
  height: 45px;
  border: none;
  border-bottom: 1px solid var(--text-color);
  padding-left: 8px;
}

#recent-file-panel-search-input::placeholder {
  color: var(--text-color);
  opacity: 0.5;
}

.megamenu-menu-header {
  border-bottom: none;
}

#recent-file-panel-action-btn {
  background: none;
  border: none;
}

#recent-file-panel-action-btn-container {
  float: none;
  display: inline-block;
}

#top-titlebar .toolbar-icon.btn.hover,
#top-titlebar .toolbar-icon.btn:hover {
  background-color: var(--btn-hover-bg-color);
  color: var(--text-color);
}

.megamenu-menu-panel .btn:hover {
  background-color: var(--btn-hover-bg-color) !important;
  color: var(--text-color);
}

#recent-file-panel tbody tr:nth-child(2n-1),
.megamenu-menu-panel table thead,
.megamenu-menu-panel table tr {
  background-color: transparent;
}

.megamenu-menu-panel table {
  font-weight: normal;
}

#megamenu-back-btn {
  color: var(--text-color);
  border: 1px solid var(--text-color);
}

.megamenu-menu-header #megamenu-menu-header-title {
  color: var(--text-color);
}

header,
.context-menu,
.megamenu-content,
footer {
  font-family: var(--text-font);
}

.ty-preferences select {
  padding-left: 2px;
}

.preference-item-hint {
  font-size: 14px;
}

a.ty-link {
  color: var(--a-color);
  margin: 0 0.2rem;
}

/**
    code render
    Name: IntelliJ IDEA darcula theme
    From IntelliJ IDEA by JetBrains
 */
.cm-s-inner.CodeMirror {
  background: none;
  color: var(--code-block-color);
}

.cm-s-inner span.cm-meta {
  color: #bbb529;
}

.cm-s-inner span.cm-number {
  color: #6897bb;
}

.cm-s-inner span.cm-keyword {
  color: #cc7832;
}

.cm-s-inner span.cm-def {
  color: #ffd760;
}

.cm-s-inner span.cm-variable {
  color: var(--code-block-color);
}

.cm-s-inner span.cm-variable-2 {
  color: var(--code-block-color);
}

.cm-s-inner span.cm-variable-3 {
  color: #9876aa;
}

.cm-s-inner span.cm-type {
  color: #aabbcc;
}

.cm-s-inner span.cm-property {
  color: #ffc66d;
}

.cm-s-inner span.cm-operator {
  color: var(--code-block-color);
}

.cm-s-inner span.cm-string {
  color: #6a8759;
}

.cm-s-inner span.cm-string-2 {
  color: #6a8759;
}

.cm-s-inner span.cm-comment {
  color: #787878;
}

.cm-s-inner span.cm-link {
  color: #cc7832;
}

.cm-s-inner span.cm-atom {
  color: #cc7832;
}

.cm-s-inner span.cm-error {
  color: #bc3f3c;
}

.cm-s-inner span.cm-tag {
  color: #e8bf6a;
}

.cm-s-inner span.cm-quote {
  color: #a6e22e;
}

.cm-s-inner span.cm-attribute {
  color: #9876aa;
}

.cm-s-inner span.cm-qualifier {
  color: #6a8759;
}

.cm-s-inner span.cm-bracket {
  color: #e8bf6a;
}

.cm-s-inner span.cm-builtin {
  color: #ff9e59;
}

.cm-s-inner span.cm-special {
  color: #ff9e59;
}

.cm-s-inner span.cm-matchhighlight {
  color: #ffffff;
  background-color: rgba(50, 89, 48, 0.7);
  font-weight: normal;
}

.cm-s-inner span.cm-searching {
  color: #ffffff;
  background-color: rgba(61, 115, 59, 0.7);
  font-weight: normal;
}

.cm-s-inner .CodeMirror-gutters {
  border-right: 1px solid rgba(120, 120, 120, 0.3);
  background: var(--code-block-bg-color);
}

.cm-s-inner .CodeMirror-linenumber {
  color: #585b5d;
}

.cm-s-inner .CodeMirror-matchingbracket {
  background-color: #3b514d;
  color: #ffef28 !important;
}

.cm-s-inner .CodeMirror-selected {
  background: #214283 !important;
}

.cm-s-inner .CodeMirror-selectedtext {
  background: #214283 !important;
}

.cm-s-typora-default .CodeMirror-selectedtext {
  background: var(--select-text-bg-color) !important;
}

.cm-overlay.CodeMirror-selectedtext {
  background: var(--select-text-bg-color) !important;
}

.cm-s-inner div.CodeMirror-cursor {
  border-left: 1px solid var(--code-block-color);
}

.cm-s-inner .cm-header,
.cm-s-inner.cm-header {
  color: #ffd760;
  font-weight: normal;
}


 @media print { @page {margin: 0 0 0 0;} body.typora-export {padding-left: 0; padding-right: 0;} #write {padding:0;}}
</style><title>resume-md</title>
</head>
<body class='typora-export'><div class='typora-export-content'>
<div id='write'  class=''><h1 id='潘慧文'><span>潘慧文</span></h1><p><strong><span>移动开发工程师 | 大前端开发工程师</span></strong></p><p><span>📱 15354872767 (微信同号) | ✉️ </span><a href='mailto:<EMAIL>' target='_blank' class='url'><EMAIL></a><span> | 📍 北京/呼和浩特 | 🎂 1992年2月 | 🧑🏻‍💻 男</span></p><hr /><h2 id='💼-工作经验'><span>💼 工作经验</span></h2><h3 id='移动开发工程师-|-内蒙古博睿达网络科技有限公司'><span>移动开发工程师 | 内蒙古博睿达网络科技有限公司</span></h3><p><strong><span>2024年6月 - 至今 (在职)</span></strong></p><h4 id='核心项目成果'><span>核心项目成果</span></h4><ul><li><p><strong><span>特教公益小程序</span></strong><span> (2024至今)</span></p><ul><li><p><span>与厦门特教基金会合作，采用uni-app开发公益类小程序</span></p></li><li><p><span>实现标准模块化设计，支持站点配置和内容结构化配置</span></p></li><li><p><span>兼容用户、管理员双身份，涵盖益卖商城、捐赠系统、社区活动、IM等功能</span></p></li><li><p><strong><span>技术难点</span></strong><span>: 多身份权限控制、模块化配置系统、跨平台兼容性优化</span></p></li><li><p><strong><span>成果</span></strong><span>: 成功上线并获得基金会认可，提升公益服务数字化水平</span></p></li></ul></li><li><p><strong><span>企业内部服务小程序矩阵</span></strong><span> (2024至今)</span></p><ul><li><p><span>开发集团企业内部服务系统和员工优惠购平台</span></p></li><li><p><span>包含内部报修、会议室预定、餐饮预订等效率工具</span></p></li><li><p><strong><span>技术难点</span></strong><span>: 企业级权限管理、多业务模块集成、数据安全保障</span></p></li><li><p><strong><span>成果</span></strong><span>: 提升企业内部运营效率，获得客户好评</span></p></li></ul></li></ul><h4 id='技术架构与项目管理'><span>技术架构与项目管理</span></h4><ul><li><p><strong><span>小程序开发</span></strong><span>: uni-app跨平台开发，支持模块化配置和多身份权限</span></p></li><li><p><strong><span>架构设计</span></strong><span>: 标准化模块设计，内容结构化配置，可复用组件库</span></p></li><li><p><strong><span>项目管理</span></strong><span>: 主导移动端项目开发，定制化企业内部服务解决方案</span></p></li></ul><h4 id='主要产品线'><span>主要产品线</span></h4><ul><li><p><span>特教公益类小程序（与厦门特教基金会合作）</span></p></li><li><p><span>企业内部服务小程序</span></p></li><li><p><span>定制化内购小程序</span></p></li></ul><h3 id='移动开发工程师-|-北京牛投邦科技咨询有限公司'><span>移动开发工程师 | 北京牛投邦科技咨询有限公司</span></h3><p><strong><span>2018年8月 - 2024年4月 (5年8个月)</span></strong></p><h4 id='核心项目成果-2'><span>核心项目成果</span></h4><ul><li><p><strong><span>多角色权限系统重构</span></strong><span> (2020-2024)</span></p><ul><li><p><span>主导多家头部基金客户端从单角色升级为多角色多权限架构</span></p></li><li><p><span>设计并实现复杂的角色切换、权限控制、状态管理系统</span></p></li><li><p><span>通过设计模式优化，实现7种不同权限判别动作的统一管理</span></p></li><li><p><strong><span>技术难点</span></strong><span>: 多角色动态切换、权限判别逻辑、状态同步、数据缓存</span></p></li><li><p><strong><span>成果</span></strong><span>: 项目如期交付，获得客户高度认可</span></p></li></ul></li><li><p><strong><span>性能优化专项</span></strong><span> (2020-2024)</span></p><ul><li><p><span>通过代码分包、预加载、启动流程优化将初始化时间</span><strong><span>减少60%+</span></strong></p></li><li><p><span>实施骨架屏、数据缓存、本地化存储等用户体验优化</span></p></li><li><p><strong><span>成果</span></strong><span>: 显著提升加载速度和用户体验</span></p></li></ul></li></ul><h4 id='技术架构与项目管理-2'><span>技术架构与项目管理</span></h4><ul><li><p><strong><span>移动端</span></strong><span>: React Native客户端开发，支持热更新、支付、社会化分享、应用商店送审</span></p></li><li><p><strong><span>小程序</span></strong><span>: 微信原生、Taro、uni-app多平台开发，模板化架构设计</span></p></li><li><p><strong><span>后台管理</span></strong><span>: PC端管理系统开发，完整的前后端分离架构</span></p></li><li><p><strong><span>项目模板化</span></strong><span>: 主导基于WBS的项目模板开发，提升团队开发效率</span></p></li></ul><h4 id='主要产品线-2'><span>主要产品线</span></h4><ul><li><p><span>智能营销获客管理系统、栗子理财师、理财顾问云</span></p></li><li><p><span>获客排行榜小程序、鹰眼获客能手小程序</span></p></li><li><p><span>多家头部基金公司移动端解决方案</span></p></li></ul><h3 id='移动开发工程师-|-北京天云智慧科技有限公司'><span>移动开发工程师 | 北京天云智慧科技有限公司</span></h3><p><strong><span>2017年6月 - 2018年8月 (1年2个月)</span></strong></p><h4 id='核心项目成果-3'><span>核心项目成果</span></h4><ul><li><p><strong><span>内部招聘移动应用</span></strong><span> (2017-2018)</span></p><ul><li><p><span>独立开发内部招聘客户端和招聘管理端</span></p></li><li><p><span>React Native开发，从0到1完成产品交付</span></p></li><li><p><strong><span>技术难点</span></strong><span>: 独立负责移动端架构设计、前后端数据交互、用户体验优化</span></p></li><li><p><strong><span>成果</span></strong><span>: 成功交付完整的移动招聘管理系统，提升企业招聘效率</span></p></li></ul></li></ul><h4 id='技术架构与项目管理-3'><span>技术架构与项目管理</span></h4><ul><li><p><strong><span>移动端</span></strong><span>: React Native开发，涵盖客户端和管理端</span></p></li><li><p><strong><span>项目管理</span></strong><span>: 独立负责产品设计、开发、测试、上线全流程</span></p></li><li><p><strong><span>技术栈</span></strong><span>: JavaScript、React Native</span></p></li></ul><h4 id='主要产品线-3'><span>主要产品线</span></h4><ul><li><p><span>内部招聘客户端</span></p></li><li><p><span>招聘管理端</span></p></li></ul><hr /><h2 id='🛠️-技术栈'><span>🛠️ 技术栈</span></h2><h3 id='前端技术'><span>前端技术</span></h3><ul><li><p><strong><span>移动端</span></strong><span>: React Native、微信小程序、Taro、uni-app</span></p></li><li><p><strong><span>Web前端</span></strong><span>: React、Vue、JavaScript (ES6+)、HTML5、CSS3</span></p></li><li><p><strong><span>后端技术</span></strong><span>: Node.js</span></p></li><li><p><strong><span>构建工具</span></strong><span>: Webpack、Vite、CI/CD流水线</span></p></li></ul><h3 id='开发工具与流程'><span>开发工具与流程</span></h3><ul><li><p><strong><span>IDE</span></strong><span>: WebStorm、VS Code</span></p></li><li><p><strong><span>版本控制</span></strong><span>: Git (团队协作、代码审查)</span></p></li><li><p><strong><span>项目管理</span></strong><span>: 敏捷开发、结果导向的项目交付</span></p></li></ul><h3 id='核心能力'><span>核心能力</span></h3><ul><li><p><span>数据结构与算法、设计模式应用</span></p></li><li><p><span>独立开发能力、代码规范化</span></p></li><li><p><span>性能优化、架构设计</span></p></li></ul><hr /><h2 id='🏢-行业经验'><span>🏢 行业经验</span></h2><p><strong><span>金融科技领域</span></strong><span> </span></p><ul><li><p><span>财富管理、证券、保险、基金、理财产品</span></p></li><li><p><span>熟悉金融业务流程和合规要求</span></p></li><li><p><span>具备完整的金融产品开发生命周期经验</span></p></li></ul><p><strong><span>教育科技与公益领域</span></strong></p><ul><li><p><span>特教公益小程序开发与运营</span></p></li><li><p><span>企业定制化服务解决方案</span></p></li><li><p><span>内部效率工具开发</span></p></li></ul><hr /><h2 id='🎯-核心优势'><span>🎯 核心优势</span></h2><h3 id='技术能力'><span>技术能力</span></h3><ul><li><p><span>擅长移动端开发和架构设计</span></p></li><li><p><span>具备从0到1的产品开发经验</span></p></li><li><p><span>熟悉复杂业务场景的技术解决方案设计</span></p></li></ul><h3 id='问题解决能力'><span>问题解决能力</span></h3><ul><li><p><span>成功解决多角色权限系统的复杂业务逻辑</span></p></li><li><p><span>通过技术手段显著提升产品性能和用户体验</span></p></li><li><p><span>具备较强的技术难点攻克能力</span></p></li></ul><h3 id='团队协作'><span>团队协作</span></h3><ul><li><p><span>良好的沟通协调能力，跨部门合作经验丰富</span></p></li><li><p><span>责任心强，以结果为导向的工作方式</span></p></li><li><p><span>有内部技术分享经验，推动团队技术成长</span></p></li></ul><hr /><h2 id='🎓-教育背景'><span>🎓 教育背景</span></h2><p><strong><span>本科学士学位</span></strong><span> | 软件工程专业 | 2013.9 - 2017.6</span>
<span>内蒙古财经大学</span></p><hr /><h2 id='📋-求职信息'><span>📋 求职信息</span></h2><ul><li><p><strong><span>期望职位</span></strong><span>: 移动开发工程师 / 大前端开发工程师</span></p></li><li><p><strong><span>工作地点</span></strong><span>: 北京 / 呼和浩特</span></p></li><li><p><strong><span>到岗时间</span></strong><span>: 5-7个工作日</span></p></li><li><p><strong><span>工作年限</span></strong><span>: 8年 (2016年6月至今)</span></p></li></ul></div></div>
</body>
</html>